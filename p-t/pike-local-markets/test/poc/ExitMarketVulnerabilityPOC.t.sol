// SPDX-License-Identifier: MIT
pragma solidity 0.8.28;

import {TestLocal} from "@helpers/TestLocal.sol";
import {IPToken} from "@modules/pToken/PTokenModule.sol";
import {IRiskEngine} from "@modules/riskEngine/RiskEngineModule.sol";
import {MockOracle} from "@mocks/MockOracle.sol";

/**
 * @title ExitMarketVulnerabilityPOC
 * @notice Proof of Concept demonstrating the state inconsistency vulnerability in exitMarket function
 * @dev This POC demonstrates that when a user exits a market as collateral while having outstanding borrows,
 *      the pToken remains in their accountAssets array, creating state inconsistency.
 */
contract ExitMarketVulnerabilityPOC is TestLocal {
    IPToken pUSDC;
    IPToken pWETH;
    IRiskEngine re;
    MockOracle mockOracle;

    address user;
    address liquidityProvider;

    function setUp() public {
        setDebug(false);
        setAdmin(******************************************);
        init();

        // eth price = 2000$, usdc price = 1$
        deployPToken("pike-usdc", "pUSDC", 6, 1e6, 74.5e16, 84.5e16, deployMockToken);
        deployPToken("pike-weth", "pWETH", 18, 2000e6, 72.5e16, 82.5e16, deployMockToken);

        pUSDC = getPToken("pUSDC");
        pWETH = getPToken("pWETH");
        re = getRiskEngine();
        mockOracle = MockOracle(re.oracle());

        //initial mint
        doInitialMint(pUSDC);
        doInitialMint(pWETH);

        user = makeAddr("user");
        liquidityProvider = makeAddr("liquidityProvider");

        // Provide liquidity to both markets
        doDeposit(liquidityProvider, liquidityProvider, address(pUSDC), 10000e6);
        doDeposit(liquidityProvider, liquidityProvider, address(pWETH), 10e18);
    }

    /**
     * @notice POC Test 1: Demonstrates the core vulnerability
     * @dev Shows that exitMarket leaves pToken in accountAssets when user has outstanding borrows
     */
    function test_POC_ExitMarketWithBorrowLeavesInconsistentState() public {
        // Step 1: User deposits USDC and enters market as collateral
        doDepositAndEnter(user, user, address(pUSDC), 2000e6);
        
        // Verify initial state
        IPToken[] memory assetsBefore = re.getAssetsIn(user);
        assertEq(assetsBefore.length, 1, "Should have 1 asset initially");
        assertEq(address(assetsBefore[0]), address(pUSDC), "Should be pUSDC");
        assertTrue(re.checkCollateralMembership(user, pUSDC), "Should be collateral member");
        assertFalse(re.checkBorrowMembership(user, pUSDC), "Should not be borrow member initially");

        // Step 2: User borrows USDC (same asset they deposited)
        doBorrow(user, user, address(pUSDC), 500e6);
        
        // Verify borrow state
        assertTrue(re.checkBorrowMembership(user, pUSDC), "Should be borrow member after borrowing");
        IPToken[] memory assetsAfterBorrow = re.getAssetsIn(user);
        assertEq(assetsAfterBorrow.length, 1, "Should still have 1 asset after borrowing");

        // Step 3: User exits market as collateral (but still has outstanding borrow)
        vm.prank(user);
        re.exitMarket(address(pUSDC));

        // Step 4: Verify the vulnerability - inconsistent state
        assertFalse(re.checkCollateralMembership(user, pUSDC), "Should not be collateral member after exit");
        assertTrue(re.checkBorrowMembership(user, pUSDC), "Should still be borrow member");
        
        // VULNERABILITY: pToken remains in accountAssets despite not being collateral
        IPToken[] memory assetsAfterExit = re.getAssetsIn(user);
        assertEq(assetsAfterExit.length, 1, "VULNERABILITY: pToken still in accountAssets");
        assertEq(address(assetsAfterExit[0]), address(pUSDC), "VULNERABILITY: pUSDC still in assets");

        // Step 5: Verify the inconsistency affects liquidity calculations
        (, uint256 liquidity, uint256 shortfall) = re.getAccountLiquidity(user);
        
        // The user should have no collateral value since they exited the market
        // but the liquidity calculation still includes this asset because it's in accountAssets
        console.log("Liquidity after exit (should be 0 but isn't):", liquidity);
        console.log("Shortfall after exit:", shortfall);
    }

    /**
     * @notice POC Test 2: Demonstrates the cleanup works correctly when no borrow exists
     * @dev Shows that exitMarket correctly removes pToken from accountAssets when amountOwed == 0
     */
    function test_POC_ExitMarketWithoutBorrowCleansUpCorrectly() public {
        // Step 1: User deposits and enters market
        doDepositAndEnter(user, user, address(pUSDC), 2000e6);
        
        // Verify initial state
        IPToken[] memory assetsBefore = re.getAssetsIn(user);
        assertEq(assetsBefore.length, 1, "Should have 1 asset initially");
        assertTrue(re.checkCollateralMembership(user, pUSDC), "Should be collateral member");

        // Step 2: User exits market (no outstanding borrows)
        vm.prank(user);
        re.exitMarket(address(pUSDC));

        // Step 3: Verify correct cleanup
        assertFalse(re.checkCollateralMembership(user, pUSDC), "Should not be collateral member");
        assertFalse(re.checkBorrowMembership(user, pUSDC), "Should not be borrow member");
        
        // Correctly removes pToken from accountAssets when no borrow exists
        IPToken[] memory assetsAfter = re.getAssetsIn(user);
        assertEq(assetsAfter.length, 0, "Should have 0 assets after exit without borrow");
    }

    /**
     * @notice POC Test 3: Demonstrates repayBorrowVerify eventually cleans up the state
     * @dev Shows that the inconsistency is resolved when the borrow is fully repaid
     */
    function test_POC_RepayBorrowVerifyEventuallyFixesInconsistency() public {
        // Step 1: Create the inconsistent state (same as test 1)
        doDepositAndEnter(user, user, address(pUSDC), 2000e6);
        doBorrow(user, user, address(pUSDC), 500e6);
        
        vm.prank(user);
        re.exitMarket(address(pUSDC));

        // Verify inconsistent state exists
        assertFalse(re.checkCollateralMembership(user, pUSDC), "Should not be collateral member");
        assertTrue(re.checkBorrowMembership(user, pUSDC), "Should be borrow member");
        IPToken[] memory assetsBeforeRepay = re.getAssetsIn(user);
        assertEq(assetsBeforeRepay.length, 1, "pToken still in accountAssets");

        // Step 2: Fully repay the borrow
        doRepay(user, user, address(pUSDC), type(uint256).max); // Repay all

        // Step 3: Verify repayBorrowVerify cleaned up the state
        assertFalse(re.checkCollateralMembership(user, pUSDC), "Should not be collateral member");
        assertFalse(re.checkBorrowMembership(user, pUSDC), "Should not be borrow member after full repay");
        
        // repayBorrowVerify correctly removes pToken from accountAssets
        IPToken[] memory assetsAfterRepay = re.getAssetsIn(user);
        assertEq(assetsAfterRepay.length, 0, "Should have 0 assets after full repay");
    }

    /**
     * @notice POC Test 4: Demonstrates impact on liquidity calculations with multiple assets
     * @dev Shows how the vulnerability affects risk calculations when user has multiple assets
     */
    function test_POC_MultipleAssetsLiquidityImpact() public {
        // Step 1: User deposits in two different markets
        doDepositAndEnter(user, user, address(pUSDC), 2000e6); // $2000 USDC
        doDepositAndEnter(user, user, address(pWETH), 1e18);   // 1 WETH (~$2000)

        // Step 2: User borrows from USDC market
        doBorrow(user, user, address(pUSDC), 500e6);

        // Step 3: User exits USDC market as collateral but keeps WETH
        vm.prank(user);
        re.exitMarket(address(pUSDC));

        // Step 4: Verify state inconsistency
        assertFalse(re.checkCollateralMembership(user, pUSDC), "Should not be USDC collateral member");
        assertTrue(re.checkCollateralMembership(user, pWETH), "Should still be WETH collateral member");
        assertTrue(re.checkBorrowMembership(user, pUSDC), "Should still be USDC borrow member");

        // VULNERABILITY: Both assets remain in accountAssets
        IPToken[] memory assets = re.getAssetsIn(user);
        assertEq(assets.length, 2, "VULNERABILITY: Both assets still in accountAssets");

        // Step 5: Check liquidity calculation impact
        (, uint256 liquidity, uint256 shortfall) = re.getAccountLiquidity(user);
        
        console.log("Assets in accountAssets:", assets.length);
        console.log("Liquidity with inconsistent state:", liquidity);
        console.log("Shortfall with inconsistent state:", shortfall);
        
        // The liquidity calculation will iterate over both assets in accountAssets,
        // but only WETH should contribute to collateral value
    }

    /**
     * @notice POC Test 5: Edge case - Exit market that was never used as collateral but has borrow
     * @dev Tests the edge case where user borrows without using the asset as collateral
     */
    function test_POC_ExitMarketNeverCollateralWithBorrow() public {
        // Step 1: User deposits WETH as collateral
        doDepositAndEnter(user, user, address(pWETH), 1e18);

        // Step 2: User borrows USDC (without depositing USDC first)
        doBorrow(user, user, address(pUSDC), 500e6);

        // Verify state before exit attempt
        assertTrue(re.checkCollateralMembership(user, pWETH), "Should be WETH collateral member");
        assertFalse(re.checkCollateralMembership(user, pUSDC), "Should not be USDC collateral member");
        assertTrue(re.checkBorrowMembership(user, pUSDC), "Should be USDC borrow member");

        IPToken[] memory assetsBefore = re.getAssetsIn(user);
        assertEq(assetsBefore.length, 2, "Should have 2 assets (WETH + USDC)");

        // Step 3: Try to exit USDC market (user was never collateral member)
        vm.prank(user);
        re.exitMarket(address(pUSDC)); // This should return early

        // Step 4: Verify state remains unchanged (early return case)
        assertFalse(re.checkCollateralMembership(user, pUSDC), "Should still not be USDC collateral member");
        assertTrue(re.checkBorrowMembership(user, pUSDC), "Should still be USDC borrow member");

        IPToken[] memory assetsAfter = re.getAssetsIn(user);
        assertEq(assetsAfter.length, 2, "Should still have 2 assets");
    }
}
