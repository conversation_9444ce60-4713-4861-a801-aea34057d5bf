{"name": "@uniswap/v3-core", "description": "🦄 Core smart contracts of Uniswap V3", "license": "BUSL-1.1", "publishConfig": {"access": "public"}, "version": "1.0.0", "homepage": "https://uniswap.org", "keywords": ["uniswap", "core", "v3"], "repository": {"type": "git", "url": "https://github.com/Uniswap/uniswap-v3-core"}, "files": ["contracts/interfaces", "contracts/libraries", "artifacts/contracts/UniswapV3Factory.sol/UniswapV3Factory.json", "artifacts/contracts/UniswapV3Pool.sol/UniswapV3Pool.json", "artifacts/contracts/interfaces/**/*.json", "!artifacts/contracts/interfaces/**/*.dbg.json"], "engines": {"node": ">=10"}, "dependencies": {}, "devDependencies": {"@nomiclabs/hardhat-ethers": "^2.0.2", "@nomiclabs/hardhat-etherscan": "^2.1.1", "@nomiclabs/hardhat-waffle": "^2.0.1", "@typechain/ethers-v5": "^4.0.0", "@types/chai": "^4.2.6", "@types/mocha": "^5.2.7", "chai": "^4.2.0", "decimal.js": "^10.2.1", "ethereum-waffle": "^3.0.2", "ethers": "^5.0.8", "hardhat": "^2.2.0", "hardhat-typechain": "^0.3.5", "mocha": "^6.2.2", "mocha-chai-jest-snapshot": "^1.1.0", "prettier": "^2.0.5", "prettier-plugin-solidity": "^1.0.0-alpha.59", "solhint": "^3.2.1", "solhint-plugin-prettier": "^0.0.5", "ts-generator": "^0.1.1", "ts-node": "^8.5.4", "typechain": "^4.0.0", "typescript": "^3.7.3"}, "scripts": {"compile": "hardhat compile", "test": "hardhat test"}}