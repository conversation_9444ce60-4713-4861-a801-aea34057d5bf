checkAsserts: true
coverage: true
codeSize: 0x60000
corpusDir: echidna_e2e_mint_burn_corpus
seqLen: 10
testLimit: 100000
timeout: 600 # 10 minutes

# blacklist
filterFunctions:
  [
    'E2E_mint_burn.viewInitRandomPoolParams(uint128)',
    'E2E_mint_burn.viewMintRandomNewPosition(uint128,int24,uint24,int24)',
    'E2E_mint_burn.viewBurnRandomPositionIdx(uint128,uint128)',
    'E2E_mint_burn.viewBurnRandomPositionBurnAmount(uint128,uint128)',
  ]
