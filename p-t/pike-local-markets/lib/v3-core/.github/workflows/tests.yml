name: Tests

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v1
      - uses: actions/setup-node@v1
        with:
          node-version: 12.x

      - id: yarn-cache
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - uses: actions/cache@v1
        with:
          path: ${{ steps.yarn-cache.outputs.dir }}
          key: yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            yarn-

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      # This is required separately from yarn test because it generates the typechain definitions
      - name: Compile
        run: yarn compile

      - name: Run unit tests
        run: yarn test
